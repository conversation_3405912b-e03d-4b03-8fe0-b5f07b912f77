//
//  IMYMR2ndCardViewBeiyunPlan.m
//  ZZIMYMain
//
//  Created by ljh on 2025/8/11.
//

#import "IMYMR2ndCardViewBeiyunPlan.h"

@implementation IMYMR2ndCardViewBeiyunPlan

IMYHIVE_REGIST_CLASS(IMYMR2ndCardViewBeiyunPlan, IOCMR2ndCardViewProcotol);

+ (BOOL)canHandleCardData:(NSDictionary *)cardData {
    // 卡片分类：1经期健康度2黄金受孕期3好孕计划4产检单解读5喂奶6睡眠7发育测评
    NSInteger const type = [cardData[@"type"] integerValue];
    return type == 3;
}

- (void)onViewDidLoad {
    // 设置标题icon
    UIImage *icon = [UIImage imageNamed:@"vip_2nd_icon_beiyun_plan"];
    [self setupWithIcon:icon andTitle:@"好孕计划"];
    
    // 直接全部重建
    [self.contentView imy_removeAllSubviews];
    
    UIImageView *scoreImageView = [UIImageView new];
    scoreImageView.frame = CGRectMake(12, 0, 76, 76);
    [self.contentView addSubview:scoreImageView];
    
    UILabel *scoreLabel = [UILabel new];
    [self.contentView addSubview:scoreLabel];
    
    UILabel *bottomLabel = [UILabel new];
    bottomLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
    bottomLabel.textColor = [UIColor colorWithWhite:1 alpha:0.5];
    bottomLabel.text = @"计划已完成";
    [bottomLabel imy_sizeToFit];
    bottomLabel.imy_height = 16;
    bottomLabel.imy_centerX = scoreImageView.imy_centerX;
    bottomLabel.imy_bottom = self.contentView.imy_height - 4;
    [self.contentView addSubview:bottomLabel];
    
    // 内容数据
    NSDictionary * const contentData = self.rawData[@"data"];
    
    // "plan_status": 1, // 计划状态 0:未开始 1:进行中 2:已结束 3:去记录
    NSInteger const plan_status = [contentData[@"plan_status"] integerValue];
    if (plan_status == 1) {
        NSInteger const total_task_num = [contentData[@"total_task_num"] integerValue];
        NSInteger const done_task_num = [contentData[@"done_task_num"] integerValue];
        
        CGFloat const progress = done_task_num / (double)(MAX(1, total_task_num));
        
        NSString *barImageKey = nil;
        if (progress > 0.84) {
            barImageKey = @"mr_2nd_hyjh_6";
        } else if (progress > 0.67) {
            barImageKey = @"mr_2nd_hyjh_5";
        } else if (progress > 0.51) {
            barImageKey = @"mr_2nd_hyjh_4";
        } else if (progress > 0.34) {
            barImageKey = @"mr_2nd_hyjh_3";
        } else if (progress > 0.17) {
            barImageKey = @"mr_2nd_hyjh_2";
        } else if (progress > 0.01) {
            barImageKey = @"mr_2nd_hyjh_1";
        } else {
            barImageKey = @"mr_2nd_hyjh_0";
        }
        scoreImageView.image = [UIImage imageNamed:barImageKey];
        
        scoreLabel.font = [UIFont systemFontOfSize:19 weight:UIFontWeightMedium];
        scoreLabel.text = [NSString stringWithFormat:@"%ld/%ld", done_task_num, total_task_num];
        scoreLabel.textColor = IMY_COLOR_KEY(@"#FF4D88");
        [scoreLabel imy_sizeToFit];
        scoreLabel.center = scoreImageView.center;
        
        UILabel *tipLabel = [UILabel new];
        [self.contentView addSubview:tipLabel];
        
        // 任务已完成
        if (progress >= 1.0) {
            NSString *rightText = contentData[@"text"];
            if (!rightText.length) {
                rightText = @"真棒！今日打卡计划已完成。坚持住，小确幸在发芽！";
            }
            tipLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
            tipLabel.textColor = [UIColor whiteColor];
            tipLabel.text = rightText;
            tipLabel.numberOfLines = 3;
            tipLabel.frame = CGRectMake(110, 11, 138, 54);
        } else {
            // 任务进行中
            tipLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
            tipLabel.textColor = [UIColor colorWithWhite:1 alpha:0.5];
            tipLabel.text = @"今日待完成计划：";
            [tipLabel imy_sizeToFit];
            tipLabel.imy_height = 16;
            tipLabel.imy_left = 110;
            tipLabel.imy_top = 10;
            
            // 获取待完成任务最多两项
            NSArray * const task_list = contentData[@"task_list"];
            for (NSInteger index = 0; index < task_list.count && index < 2; index ++) {
                NSString * const text = task_list[index];
                
                UILabel *textLabel = [UILabel new];
                textLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
                textLabel.textColor = UIColor.whiteColor;
                textLabel.text = text.length > 0 ? text : @"";
                textLabel.frame = CGRectMake(120, index == 0 ? 28 : 48, 138, 18);
                [textLabel imy_sizeToFitWidth];
                [self.contentView addSubview:textLabel];
                
                UIImageView *iconView = [UIImageView new];
                iconView.imy_size = CGSizeMake(8, 8);
                iconView.image = [UIImage imageNamed:@"mr_2nd_hyjh_icon"];
                iconView.imy_centerY = textLabel.imy_centerY;
                iconView.imy_right = textLabel.imy_left - 2;
                [self.contentView addSubview:iconView];
            }
        }
    } else {
        // 兜底UI
        if (plan_status == 3) {
            scoreImageView.image = [UIImage imageNamed:@"mr_2nd_hyjh_0"];
            
            scoreLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
            scoreLabel.text = @"--";
            scoreLabel.textColor = UIColor.whiteColor;
            [scoreLabel imy_sizeToFit];
            scoreLabel.center = scoreImageView.center;
        } else {
            scoreImageView.image = [UIImage imageNamed:@"vip_2nd_empty_beiyun_plan"];
            scoreLabel.hidden = YES;
            bottomLabel.hidden = YES;
        }
        
        NSString *rightText = contentData[@"text"];
        if (!rightText.length) {
            if (plan_status == 2) {
                rightText = @"当前计划已结束，请重新测评获得计划内容";
            } else if (plan_status == 3) {
                rightText = @"建议补记经期情况，为您制定个性化计划";
            } else {
                rightText = @"先完成好孕测评，为您制定个性化好孕计划";
            }
        }
        
        UILabel *textLabel = [UILabel new];
        textLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        textLabel.textColor = [UIColor colorWithWhite:1 alpha:0.7];
        textLabel.numberOfLines = 2;
        textLabel.text = rightText;
        textLabel.frame = CGRectMake(110, 6, 138, 36);
        [self.contentView addSubview:textLabel];
        
        NSString *button_name = contentData[@"button_name"];
        if (!button_name.length) {
            if (plan_status == 3) {
                button_name = @"去记录";
            } else {
                button_name = @"去测评";
            }
        }
        UILabel *gotoLabel = [UILabel new];
        gotoLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        gotoLabel.textColor = IMY_COLOR_KEY(@"#FF4D88");
        gotoLabel.textAlignment = NSTextAlignmentCenter;
        gotoLabel.layer.borderColor = IMY_COLOR_KEY(@"#FF4D88").CGColor;
        gotoLabel.layer.borderWidth = 1 / SCREEN_SCALE;
        gotoLabel.layer.cornerRadius = 12;
        gotoLabel.frame = CGRectMake(110, 46, 57, 24);
        gotoLabel.text = button_name;
        [gotoLabel imy_sizeToFitWidth];
        gotoLabel.imy_width += 24;
        [self.contentView addSubview:gotoLabel];
    }
}

- (void)onViewDidClick {
    NSString *jump_uri = self.rawData[@"data"][@"jump_uri"];
    if (jump_uri.length > 0) {
        [[IMYURIManager sharedInstance] runActionWithString:jump_uri];
    } else {
        [[IMYURIManager sharedInstance] runActionWithPath:@"web/pure/common"
                                                   params:@{ @"url" : @"https://tools-node.seeyouyima.com/pregnant-plan/home.html" }
                                                     info:nil];
    }
}

- (NSDictionary *)onReportParams {
    NSDictionary * const contentData = self.rawData[@"data"];
    NSInteger const plan_status = [contentData[@"plan_status"] integerValue];
    NSInteger const total_task_num = [contentData[@"total_task_num"] integerValue];
    NSInteger const done_task_num = [contentData[@"done_task_num"] integerValue];
    
    NSString *public_type = nil;
    if (plan_status == 1) {
        if (done_task_num >= total_task_num) {
            public_type = @"完成打卡";
        } else {
            public_type = @"待完成计划";
        }
    } else if (plan_status == 2) {
        public_type = @"重新测评";
    } else if (plan_status == 3) {
        public_type = @"去记录";
    } else {
        public_type = @"去测评";
    }
    return @{
        @"public_type" : public_type ?: @"",
    };
}

@end
