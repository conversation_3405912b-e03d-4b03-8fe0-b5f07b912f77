//
//  IMYMR2ndCardViewBeiyunGolden.m
//  ZZIMYMain
//
//  Created by ljh on 2025/8/11.
//

#import "IMYMR2ndCardViewBeiyunGolden.h"

@implementation IMYMR2ndCardViewBeiyunGolden

IMYHIVE_REGIST_CLASS(IMYMR2ndCardViewBeiyunGolden, IOCMR2ndCardViewProcotol);

+ (BOOL)canHandleCardData:(NSDictionary *)cardData {
    // 卡片分类：1经期健康度2黄金受孕期3好孕计划4产检单解读5喂奶6睡眠7发育测评
    NSInteger const type = [cardData[@"type"] integerValue];
    return type == 2;
}

- (void)onViewDidLoad {
    // 设置标题icon
    UIImage *icon = [UIImage imageNamed:@"vip_2nd_icon_beiyun_golden"];
    [self setupWithIcon:icon andTitle:@"黄金受孕期"];
    
    // 直接全部重建
    [self.contentView imy_removeAllSubviews];
    
    UILabel *scoreLabel = [UILabel new];
    scoreLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
    [self.contentView addSubview:scoreLabel];
    
    UILabel *bottomLabel = [UILabel new];
    bottomLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
    bottomLabel.textColor = [UIColor colorWithWhite:1 alpha:0.5];
    [self.contentView addSubview:bottomLabel];
    
    // 内容数据
    NSDictionary * const contentData = self.rawData[@"data"];
    
    // 是否有数据
    BOOL const has_data = [contentData[@"has_data"] boolValue];
    if (has_data) {
        // 周期
        NSString * const user_stage = contentData[@"user_stage"];
        scoreLabel.text = (user_stage.length > 0 ? user_stage : @"--");
        scoreLabel.textColor = IMY_COLOR_KEY(@"#FF4D88");
        [scoreLabel imy_sizeToFit];
        scoreLabel.imy_height = 21;
        scoreLabel.imy_top = 19;
        scoreLabel.imy_centerX = 50;
        
        // 周期天数
        NSString * const period_days = contentData[@"period_days"];
        bottomLabel.text = period_days.length > 0 ? period_days : @"周期第--天";
        [bottomLabel imy_sizeToFit];
        bottomLabel.imy_top = scoreLabel.imy_bottom + 2;
        bottomLabel.imy_centerX = scoreLabel.imy_centerX;
        
        // 获取标签最多两项
        NSArray * const label_info = contentData[@"label_info"];
        for (NSInteger index = 0; index < label_info.count && index < 2; index ++) {
            NSDictionary * const item = label_info[index];
            
            NSString * const text = item[@"text"];
            NSString * const label = item[@"label"];
            NSInteger const labelColor = [item[@"label_color"] integerValue];
            
            UILabel *textLabel = [UILabel new];
            textLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
            textLabel.textColor = UIColor.whiteColor;
            textLabel.text = text.length > 0 ? text : @"";
            textLabel.frame = CGRectMake(110, index == 0 ? 18 : 40, 0, 18);
            [textLabel imy_sizeToFitWidth];
            [self.contentView addSubview:textLabel];
            
            UILabel *tagLabel = [UILabel new];
            tagLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
            tagLabel.text = label.length > 0 ? label : @"";
            tagLabel.backgroundColor = [UIColor colorWithWhite:0 alpha:0.15];
            tagLabel.textAlignment = NSTextAlignmentCenter;
            [tagLabel imy_drawAllCornerRadius:4];
            tagLabel.frame = CGRectMake(0, 0, 0, 16);
            [tagLabel imy_sizeToFitWidth];
            tagLabel.imy_width += 8;
            tagLabel.imy_left = textLabel.imy_right + 2;
            tagLabel.imy_centerY = textLabel.imy_centerY;
            if (!label.length) {
                tagLabel.hidden = YES;
            }
            [self.contentView addSubview:tagLabel];
            
            // 0:灰色 1:绿色 2:黄色 3:红色
            switch (labelColor) {
                case 1:
                    tagLabel.textColor = IMY_COLOR_KEY(@"#29CC5F");
                    break;
                case 2:
                    tagLabel.textColor = IMY_COLOR_KEY(@"#FF8833");
                    break;
                case 3:
                    tagLabel.textColor = IMY_COLOR_KEY(@"#FF4D4D");
                    break;
                default:
                    tagLabel.textColor = IMY_COLOR_KEY(@"#999999");
                    break;
            }
        }
    } else {
        // 兜底UI
        scoreLabel.text = @"--";
        scoreLabel.textColor = UIColor.whiteColor;
        [scoreLabel imy_sizeToFit];
        scoreLabel.imy_height = 21;
        scoreLabel.imy_top = 19;
        scoreLabel.imy_centerX = 50;
        
        // 周期天数
        bottomLabel.text = @"周期第--天";
        [bottomLabel imy_sizeToFit];
        bottomLabel.imy_top = scoreLabel.imy_bottom + 2;
        bottomLabel.imy_centerX = scoreLabel.imy_centerX;
        
        UILabel *textLabel = [UILabel new];
        textLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        textLabel.textColor = [UIColor colorWithWhite:1 alpha:0.7];
        textLabel.numberOfLines = 2;
        textLabel.text = @"补记经期情况，为您推算黄金受孕期";
        textLabel.frame = CGRectMake(110, 6, 138, 36);
        [self.contentView addSubview:textLabel];
        
        UILabel *gotoLabel = [UILabel new];
        gotoLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        gotoLabel.textColor = IMY_COLOR_KEY(@"#FF4D88");
        gotoLabel.textAlignment = NSTextAlignmentCenter;
        gotoLabel.layer.borderColor = IMY_COLOR_KEY(@"#FF4D88").CGColor;
        gotoLabel.layer.borderWidth = 1 / SCREEN_SCALE;
        gotoLabel.layer.cornerRadius = 12;
        gotoLabel.frame = CGRectMake(110, 46, 57, 24);
        gotoLabel.text = @"去记录";
        [self.contentView addSubview:gotoLabel];
    }
}

- (void)onViewDidClick {
    // 黄金备孕期
    [[IMYURIManager sharedInstance] runActionWithPath:@"secondFloorClick"
                                               params:@{ @"id" : @2005 }
                                                 info:nil];
}

- (NSDictionary *)onReportParams {
    NSDictionary * const contentData = self.rawData[@"data"];
    BOOL const has_data = [contentData[@"has_data"] boolValue];
    NSString * const user_stage = contentData[@"user_stage"];
    
    NSString *public_type = nil;
    if (has_data && user_stage.length > 0) {
        public_type = user_stage;
    } else {
        public_type = @"去记录";
    }
    return @{
        @"public_type" : public_type ?: @"",
    };
}

@end
