//
//  IMYMR2ndCardView.h
//  ZZIMYMain
//
//  Created by ljh on 2025/8/8.
//

#import <UIKit/UIKit.h>
#import <IMYBaseKit/IMYBaseKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface IMYMR2ndCardView : UIView

/// 顶部栏，一般无需修改
@property (nonatomic, strong, readonly) UIView *topView;

/// 内容视图，子类在此添加更多内容
@property (nonatomic, strong, readonly) UIView *contentView;

/// 数据源
@property (nonatomic, copy, readonly) NSDictionary *rawData;

/// 卡片顺序
@property (nonatomic, assign) NSInteger cardIndex;

/// 楼层
@property (nonatomic, assign) NSInteger biFloor;

/// 初始化
- (void)setupWithData:(NSDictionary *)rawData;

/// 设置标题和图标
- (void)setupWithIcon:(UIImage *)icon andTitle:(NSString *)title;

@end

#pragma mark - 子类重载用

/// IOC依赖注入
@protocol IOCMR2ndCardViewProcotol <NSObject>

/// 判断可处理的数据
+ (BOOL)canHandleCardData:(NSDictionary *)cardData;

@end

/// 对应生命周期回调
@interface IMYMR2ndCardView () <IOCMR2ndCardViewProcotol>

/// 当外部设置完数据源后调用
- (void)onViewDidLoad;

/// 卡片点击事件
- (void)onViewDidClick;

/// ga追加的埋点信息
- (NSDictionary *)onReportParams;

@end

NS_ASSUME_NONNULL_END
