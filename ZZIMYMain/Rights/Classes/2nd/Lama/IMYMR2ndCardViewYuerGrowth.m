//
//  IMYMR2ndCardViewYuerGrowth.m
//  ZZIMYMain
//
//  Created by ljh on 2025/8/11.
//

#import "IMYMR2ndCardViewYuerGrowth.h"

@implementation IMYMR2ndCardViewYuerGrowth

IMYHIVE_REGIST_CLASS(IMYMR2ndCardViewYuerGrowth, IOCMR2ndCardViewProcotol);

+ (BOOL)canHandleCardData:(NSDictionary *)cardData {
    // 卡片分类：1经期健康度2黄金受孕期3好孕计划4产检单解读5喂奶6睡眠7发育测评
    NSInteger const type = [cardData[@"type"] integerValue];
    return type == 7;
}

- (void)onViewDidLoad {
    // 设置标题icon
    UIImage *icon = [UIImage imageNamed:@"vip_2nd_icon_yuer_growth"];
    [self setupWithIcon:icon andTitle:@"发育测评"];
    
    // 直接全部重建
    [self.contentView imy_removeAllSubviews];
    
    UIImageView *scoreImageView = [UIImageView new];
    scoreImageView.frame = CGRectMake(12, 0, 76, 76);
    [self.contentView addSubview:scoreImageView];
    
    UILabel *scoreLabel = [UILabel new];
    [self.contentView addSubview:scoreLabel];
    
    UILabel *bottomLabel = [UILabel new];
    bottomLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
    bottomLabel.textColor = [UIColor colorWithWhite:1 alpha:0.5];
    [self.contentView addSubview:bottomLabel];
    
    // 内容数据
    NSDictionary * const contentData = self.rawData[@"data"];
    
    // 显示月龄
    NSInteger const month = [contentData[@"month"] integerValue];
    bottomLabel.text = [NSString stringWithFormat:@"%ld月龄测评结果", month];
    [bottomLabel imy_sizeToFit];
    bottomLabel.imy_height = 16;
    bottomLabel.imy_centerX = scoreImageView.imy_centerX;
    bottomLabel.imy_bottom = self.contentView.imy_height - 4;
    if (!contentData[@"month"]) {
        // 无月龄字段，底部不显示
        bottomLabel.hidden = YES;
    }
    
    // 判断是否有评测结果
    BOOL const has_result = [contentData[@"has_result"] boolValue];
    // 评测等级：1优秀、2良好、3中等、4偏低、5落后
    NSInteger const combined_evaluate_result = [contentData[@"combined_evaluate_result"] integerValue];
    if (has_result && combined_evaluate_result >= 1 && combined_evaluate_result <= 5) {
        
        NSString * const combined_mental_age = contentData[@"combined_mental_age"];
        NSString * const combined_developmental_quotient = contentData[@"combined_developmental_quotient"];
        
        UILabel *textLabel = [UILabel new];
        textLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        textLabel.textColor = UIColor.whiteColor;
        textLabel.text = [NSString stringWithFormat:@"综合智龄 %@", combined_mental_age];
        textLabel.frame = CGRectMake(116, 18, 132, 18);
        [self.contentView addSubview:textLabel];
        
        UILabel *text2Label = [UILabel new];
        text2Label.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        text2Label.textColor = UIColor.whiteColor;
        text2Label.text = [NSString stringWithFormat:@"综合发育商 %@", combined_developmental_quotient];
        text2Label.frame = CGRectMake(116, textLabel.imy_bottom + 4, 132, 18);
        [self.contentView addSubview:text2Label];
        
        // 只需要用 综合发育商 算分数
        double const allScore = [combined_developmental_quotient doubleValue];
        NSString *barImageKey = nil;
        if (combined_evaluate_result == 1) {
            // 优秀
            if (allScore >= 150) {
                barImageKey = @"mr_2nd_fycp_150_999";
            } else {
                barImageKey = @"mr_2nd_fycp_130_149";
            }
        } else if (combined_evaluate_result == 2) {
            // 良好
            if (allScore >= 120) {
                barImageKey = @"mr_2nd_fycp_120_129";
            } else {
                barImageKey = @"mr_2nd_fycp_110_119";
            }
        } else if (combined_evaluate_result == 3) {
            // 中等
            if (allScore >= 95) {
                barImageKey = @"mr_2nd_fycp_95_109";
            } else {
                barImageKey = @"mr_2nd_fycp_80_94";
            }
        } else if (combined_evaluate_result == 4) {
            // 偏低
            barImageKey = @"mr_2nd_fycp_70_79";
        } else if (combined_evaluate_result == 5) {
            // 落后
            if (allScore >= 30) {
                barImageKey = @"mr_2nd_fycp_30_69";
            } else {
                barImageKey = @"mr_2nd_fycp_00_30";
            }
        }
        scoreImageView.image = [UIImage imageNamed:barImageKey];
        scoreLabel.hidden = YES;
    } else {
        // 兜底UI
        scoreImageView.image = [UIImage imageNamed:@"mr_2nd_jqjkd_0"];
        
        scoreLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        scoreLabel.text = @"--";
        scoreLabel.textColor = UIColor.whiteColor;
        [scoreLabel imy_sizeToFit];
        scoreLabel.center = scoreImageView.center;
        
        UILabel *textLabel = [UILabel new];
        textLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        textLabel.textColor = UIColor.whiteColor;
        textLabel.text = [NSString stringWithFormat:@"综合智龄 %@", @"--"];
        textLabel.frame = CGRectMake(116, 5, 132, 18);
        [self.contentView addSubview:textLabel];
        
        UILabel *text2Label = [UILabel new];
        text2Label.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        text2Label.textColor = UIColor.whiteColor;
        text2Label.text = [NSString stringWithFormat:@"综合发育商 %@", @"--"];
        text2Label.frame = CGRectMake(116, textLabel.imy_bottom + 2, 132, 18);
        [self.contentView addSubview:text2Label];
        
        UILabel *gotoLabel = [UILabel new];
        gotoLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        gotoLabel.textColor = IMY_COLOR_KEY(@"#FF4D88");
        gotoLabel.textAlignment = NSTextAlignmentCenter;
        gotoLabel.layer.borderColor = IMY_COLOR_KEY(@"#FF4D88").CGColor;
        gotoLabel.layer.borderWidth = 1 / SCREEN_SCALE;
        gotoLabel.layer.cornerRadius = 12;
        gotoLabel.frame = CGRectMake(116, text2Label.imy_bottom + 2, 68, 24);
        gotoLabel.text = @"立即测评";
        [self.contentView addSubview:gotoLabel];
    }
}

- (void)onViewDidClick {
    NSString *jump_uri = self.rawData[@"data"][@"uri"];
    if (jump_uri.length > 0) {
        [[IMYURIManager sharedInstance] runActionWithString:jump_uri];
    } else {
        [[IMYURIManager sharedInstance] runActionWithPath:@"tools/addbabyId/webpure"
                                                   params:@{ @"url" : @"https://tools-node.seeyouyima.com/baby-test/full-report.html" }
                                                     info:nil];
    }
}

- (NSDictionary *)onReportParams {
    NSDictionary * const contentData = self.rawData[@"data"];
    NSInteger const combined_evaluate_result = [contentData[@"combined_evaluate_result"] integerValue];
    
    NSString *public_type = nil;
    if (combined_evaluate_result == 1) {
        public_type = @"优秀";
    } else if (combined_evaluate_result == 2) {
        public_type = @"良好";
    } else if (combined_evaluate_result == 3) {
        public_type = @"中等";
    } else if (combined_evaluate_result == 4) {
        public_type = @"偏低";
    } else if (combined_evaluate_result == 5) {
        public_type = @"落后";
    } else {
        // 服务端没给，报 去测评
        public_type = @"去测评";
    }
    
    NSString *baby_id = self.rawData[@"baby_id"];
    return @{
        @"public_type" : public_type ?: @"",
        @"baby_id" : baby_id ?: @"",
    };
}

@end
